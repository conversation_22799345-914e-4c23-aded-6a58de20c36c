import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Phone, ArrowRight, RotateCcw, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { validatePhone, validateOTP } from '../../services/authAPI';

const OTPLogin = ({ onSuccess, onClose }) => {
  const [step, setStep] = useState('phone'); // 'phone' | 'otp' | 'profile'
  const [phone, setPhone] = useState('');
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [userDetails, setUserDetails] = useState({
    first_name: '',
    last_name: '',
    email: ''
  });
  const [resendTimer, setResendTimer] = useState(0);
  const [errors, setErrors] = useState({});

  const { sendOTP, verifyOTP, resendOTP, isLoading, error, otpSent, originalPhone, clearError } = useAuth();

  // Auto-advance to OTP step when OTP is sent
  useEffect(() => {
    if (otpSent && step === 'phone') {
      console.log('Advancing to OTP step, current phone state:', phone);
      setStep('otp');
      setResendTimer(55); // 55 seconds as requested
    }
  }, [otpSent, step, phone]);

  const otpRefs = useRef([]);
  const phoneInputRef = useRef(null);

  // Timer for resend OTP
  useEffect(() => {
    let interval;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  // Focus phone input on mount
  useEffect(() => {
    if (phoneInputRef.current) {
      phoneInputRef.current.focus();
    }
  }, []);

  // Clear errors when inputs change
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => clearError(), 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  // Format phone number display
  const formatPhoneDisplay = (value) => {
    const cleaned = value.replace(/\D/g, '');
    if (cleaned.length <= 10) {
      return cleaned.replace(/(\d{5})(\d{5})/, '$1 $2');
    }
    return cleaned;
  };

  // Check if phone number is valid Indian number
  const isValidIndianNumber = (phoneNumber) => {
    const cleaned = phoneNumber.replace(/\D/g, '');
    if (cleaned.length !== 10) return false;

    // First digit should be 6, 7, 8, or 9 (valid Indian mobile number prefixes)
    const firstDigit = cleaned.charAt(0);
    return ['6', '7', '8', '9'].includes(firstDigit);
  };

  // Handle phone input
  const handlePhoneChange = (e) => {
    const value = e.target.value.replace(/\D/g, '');
    if (value.length <= 10) {
      setPhone(value);

      // Clear errors when user starts typing
      setErrors(prev => ({ ...prev, phone: '' }));

      // Provide real-time validation feedback
      if (value.length > 0) {
        if (value.length < 10) {
          setErrors(prev => ({ ...prev, phone: `Enter ${10 - value.length} more digits` }));
        } else if (value.length === 10) {
          const firstDigit = value.charAt(0);
          if (!['6', '7', '8', '9'].includes(firstDigit)) {
            setErrors(prev => ({ ...prev, phone: 'Indian mobile numbers start with 6, 7, 8, or 9' }));
          } else {
            setErrors(prev => ({ ...prev, phone: '' }));
          }
        }
      }
    }
  };

  // Handle OTP input
  const handleOTPChange = (index, value) => {
    if (!/^\d*$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      otpRefs.current[index + 1]?.focus();
    }

    setErrors(prev => ({ ...prev, otp: '' }));
  };

  // Handle OTP backspace
  const handleOTPKeyDown = (index, e) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      otpRefs.current[index - 1]?.focus();
    }
  };

  // Handle send OTP
  const handleSendOTP = async () => {
    setErrors({});

    // Prevent duplicate calls
    if (isLoading) {
      console.log('Already sending OTP, ignoring duplicate call');
      return;
    }

    // Enhanced validation for Indian phone numbers
    if (!phone || phone.trim() === '') {
      setErrors({ phone: 'Phone number is required' });
      return;
    }

    const cleaned = phone.replace(/\D/g, '');

    if (cleaned.length !== 10) {
      setErrors({ phone: 'Please enter exactly 10 digits' });
      return;
    }

    const firstDigit = cleaned.charAt(0);
    if (!['6', '7', '8', '9'].includes(firstDigit)) {
      setErrors({ phone: 'Indian mobile numbers must start with 6, 7, 8, or 9' });
      return;
    }

    console.log('Calling sendOTP with phone:', phone);
    const result = await sendOTP(phone);
    console.log('SendOTP result:', result);

    // The useEffect will handle step transition based on otpSent
    if (!result.success) {
      setErrors({ phone: result.error });
    } else {
      // Clear any previous errors on success
      clearError();
    }
  };

  // Handle verify OTP
  const handleVerifyOTP = async () => {
    setErrors({});

    const otpString = otp.join('');

    // Use originalPhone from AuthContext instead of local phone state
    const phoneToUse = originalPhone || phone;

    // Debug logging
    console.log('handleVerifyOTP called with local phone:', phone, 'originalPhone:', originalPhone, 'using:', phoneToUse, 'otp:', otpString.substring(0, 2) + '****');

    // Validate OTP
    if (!validateOTP(otpString)) {
      setErrors({ otp: 'Please enter a valid 6-digit OTP' });
      return;
    }

    const result = await verifyOTP(phoneToUse, otpString, userDetails);

    if (result.success) {
      if (result.data.is_new_user && (!userDetails.first_name || !userDetails.last_name)) {
        setStep('profile');
      } else {
        onSuccess?.(result.data);
      }
    }
  };

  // Handle profile completion
  const handleCompleteProfile = async () => {
    setErrors({});

    // Validate required fields
    const newErrors = {};
    if (!userDetails.first_name.trim()) {
      newErrors.first_name = 'First name is required';
    }
    if (!userDetails.last_name.trim()) {
      newErrors.last_name = 'Last name is required';
    }
    if (userDetails.email && !/\S+@\S+\.\S+/.test(userDetails.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    const otpString = otp.join('');
    const phoneToUse = originalPhone || phone;
    const result = await verifyOTP(phoneToUse, otpString, userDetails);

    if (result.success) {
      onSuccess?.(result.data);
    }
  };

  // Handle resend OTP
  const handleResendOTP = async () => {
    const phoneToUse = originalPhone || phone;
    const result = await resendOTP(phoneToUse);
    if (result.success) {
      setResendTimer(55); // 55 seconds as requested
      setOtp(['', '', '', '', '', '']);
      otpRefs.current[0]?.focus();
    }
  };

  return (
    <div className="w-full max-w-md mx-auto" data-step={step}>
      <AnimatePresence mode="wait">
        {/* Phone Number Step */}
        {step === 'phone' && (
          <motion.div
            key="phone"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="space-y-6 sm:space-y-8"
          >
            <div className="text-center space-y-3 sm:space-y-4">
              <motion.h2
                className="text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent tracking-tight leading-tight"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                Welcome to Wolffoxx
              </motion.h2>
              <motion.p
                className="text-gray-400 text-base sm:text-lg md:text-xl font-medium tracking-wide leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                Enter your phone number to continue
              </motion.p>
            </div>

            <motion.div
              className="space-y-5 sm:space-y-6"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <div className="space-y-3 sm:space-y-4">
                {/* <label className="block text-sm sm:text-base font-semibold text-gray-300 mb-3 sm:mb-4 flex items-center gap-2 sm:gap-3">
                  <div className="w-1 sm:w-1.5 h-4 sm:h-5 bg-orange-500 rounded-full"></div>
                  Phone Number
                </label> */}
                <div className="relative group">
                  {/* Enhanced input background with subtle animation */}
                  <div className="absolute inset-0 bg-[#2a2a2a] rounded-xl overflow-hidden z-0">
                    <div className="absolute inset-0 bg-grid-white/[0.025] bg-[length:20px_20px]"></div>
                    <div className="absolute inset-0 bg-gradient-to-r from-orange-500/0 to-orange-400/0 group-focus-within:from-orange-500/8 group-focus-within:to-orange-400/8 transition-all duration-500"></div>
                  </div>

                  {/* Country code with Indian flag and animated highlight */}
                  <div className="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none z-50">
                    <div className="flex items-center gap-2 sm:gap-3 pr-3 sm:pr-4 border-r-2 border-gray-400 group-focus-within:border-orange-400 transition-colors duration-300">
                      {/* Indian Flag - Simple and Reliable */}
                      <div className="w-6 sm:w-8 h-4 sm:h-6 border border-gray-400 rounded-sm overflow-hidden bg-white shadow-sm">
                        <div className="w-full h-1.5 sm:h-2 bg-orange-500"></div>
                        <div className="w-full h-1 sm:h-2 bg-white relative">
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="w-1 sm:w-1.5 h-1 sm:h-1.5 rounded-full bg-blue-800"></div>
                          </div>
                        </div>
                        <div className="w-full h-1.5 sm:h-2 bg-green-600"></div>
                      </div>
                      <span className="text-white font-bold text-lg sm:text-xl group-focus-within:text-orange-400 transition-colors duration-300" style={{ textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>+91</span>
                    </div>
                  </div>

                  {/* Enhanced input field */}
                  <input
                    ref={phoneInputRef}
                    type="tel"
                    value={formatPhoneDisplay(phone)}
                    onChange={handlePhoneChange}
                    placeholder="98765 43210"
                    className="relative w-full pl-24 sm:pl-28 pr-4 sm:pr-6 py-4 sm:py-5 bg-transparent border border-gray-700/60 group-focus-within:border-orange-400/60 rounded-xl text-white placeholder-gray-500 focus:ring-2 focus:ring-orange-400/25 transition-all duration-300 text-lg sm:text-xl font-medium backdrop-blur-sm z-40"
                    style={{ minHeight: '56px' }}
                  />



                </div>

                {/* Enhanced validation feedback */}
                {errors.phone && (
                  <motion.div
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-2 text-sm text-red-400 flex items-center gap-2 p-2 bg-red-500/10 border border-red-500/20 rounded-lg"
                  >
                    <AlertCircle size={16} className="text-red-400" />
                    <span className="font-medium">{errors.phone}</span>
                  </motion.div>
                )}

                {/* Success indicator for valid number */}
                {phone && phone.length === 10 && isValidIndianNumber(phone) && !errors.phone && (
                  <motion.div
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-2 text-sm text-green-400 flex items-center gap-2 p-2 bg-green-500/10 border border-green-500/20 rounded-lg"
                  >
                    <CheckCircle size={16} className="text-green-400" />
                    <span className="font-medium">Valid Indian mobile number</span>
                  </motion.div>
                )}
              </div>

              <motion.button
                onClick={handleSendOTP}
                disabled={isLoading || !phone || !isValidIndianNumber(phone)}
                className={`relative w-full group overflow-hidden transition-all duration-300 ${
                  phone && !isLoading && isValidIndianNumber(phone)
                    ? 'cursor-pointer'
                    : 'cursor-not-allowed'
                }`}
                style={{ minHeight: '52px' }}
                whileHover={{ scale: phone && !isLoading && isValidIndianNumber(phone) ? 1.02 : 1 }}
                whileTap={{ scale: phone && !isLoading && isValidIndianNumber(phone) ? 0.98 : 1 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                {/* Enhanced button background with animated gradient */}
                <div className={`absolute inset-0 rounded-xl transition-all duration-500 ${
                  phone && !isLoading && isValidIndianNumber(phone)
                    ? 'bg-gradient-to-r from-orange-600 via-orange-500 to-orange-600 bg-size-200 group-hover:bg-pos-100 border border-orange-500/50'
                    : 'bg-gradient-to-r from-gray-700 to-gray-800 border border-gray-600/30 opacity-60'
                }`} />

                {/* Animated light effect */}
                {phone && !isLoading && isValidIndianNumber(phone) && (
                  <div className="absolute inset-0">
                    <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-orange-400/0 via-orange-400/30 to-orange-400/0 opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500"></div>
                    <div className="absolute -inset-1 bg-gradient-to-r from-orange-500/0 via-orange-500/10 to-orange-500/0 rounded-xl opacity-0 group-hover:opacity-100 blur-md group-hover:animate-pulse transition-opacity duration-500"></div>
                  </div>
                )}

                {/* Subtle pattern overlay */}
                <div className="absolute inset-0 rounded-xl bg-grid-white/[0.02] bg-[length:20px_20px] opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Button content with enhanced animation */}
                <div className={`relative flex items-center justify-center gap-2 sm:gap-3 py-4 sm:py-5 px-6 sm:px-8 font-semibold text-lg sm:text-xl transition-colors duration-300 ${
                  phone && !isLoading && isValidIndianNumber(phone)
                    ? 'text-white'
                    : 'text-gray-400'
                }`}>
                  {isLoading ? (
                    <div className="flex items-center gap-3">
                      <Loader2 size={20} className="animate-spin text-orange-200" />
                      <span className="text-orange-100">Processing...</span>
                    </div>
                  ) : (
                    <>
                      <span className="group-hover:translate-x-[-2px] transition-transform duration-300">Send OTP</span>
                      <ArrowRight
                        size={20}
                        className="group-hover:translate-x-2 group-hover:scale-110 transition-all duration-300"
                      />
                    </>
                  )}
                </div>

                {/* Decorative elements */}
                {/* <div className="absolute top-2 right-2 w-1.5 h-1.5 rounded-full bg-orange-300/30 group-hover:bg-orange-300/50 transition-colors duration-300"></div>
                <div className="absolute bottom-2 left-2 w-1 h-1 rounded-full bg-orange-300/20 group-hover:bg-orange-300/40 transition-colors duration-300"></div> */}
              </motion.button>
            </motion.div>

            {error && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-5 bg-red-500/15 border border-red-500/40 rounded-xl text-red-400 text-base flex items-center gap-3 backdrop-blur-md shadow-lg shadow-red-500/10"
              >
                <AlertCircle size={18} />
                <span className="font-medium">{error}</span>
              </motion.div>
            )}
          </motion.div>
        )}

        {/* OTP Verification Step */}
        {step === 'otp' && (
          <motion.div
            key="otp"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="space-y-6 sm:space-y-8"
          >
            <div className="text-center space-y-3 sm:space-y-4">
              <motion.h2
                className="text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent tracking-tight leading-tight"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                Verify OTP
              </motion.h2>
              <motion.p
                className="text-gray-400 text-base sm:text-lg md:text-xl font-medium tracking-wide leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                Enter the 6-digit code sent to
              </motion.p>
              <motion.div
                className="flex items-center justify-center gap-2 sm:gap-3 text-lg sm:text-xl"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                {/* Indian Flag - Simple and Reliable */}
                <div className="w-6 sm:w-8 h-4 sm:h-6 border border-gray-400 rounded-sm overflow-hidden bg-white shadow-sm">
                  <div className="w-full h-1.5 sm:h-2 bg-orange-500"></div>
                  <div className="w-full h-1 sm:h-2 bg-white relative">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-1 sm:w-1.5 h-1 sm:h-1.5 rounded-full bg-blue-800"></div>
                    </div>
                  </div>
                  <div className="w-full h-1.5 sm:h-2 bg-green-600"></div>
                </div>
                <span className="text-white font-bold text-lg sm:text-xl" style={{ textShadow: '0 1px 2px rgba(0,0,0,0.8)' }}>+91</span>
                <span className="text-orange-400 font-bold tracking-wider text-lg sm:text-xl">{formatPhoneDisplay(originalPhone || phone)}</span>
              </motion.div>
            </div>

            <motion.div
              className="space-y-5 sm:space-y-6"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <div className="space-y-5 sm:space-y-6">

                <div className="flex gap-2 sm:gap-3 justify-center">
                  {otp.map((digit, index) => (
                    <motion.div
                      key={index}
                      className="relative group"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.6 + index * 0.1 }}
                    >
                      {/* Enhanced background glow effect */}

                      {/* Enhanced input field */}
                      <input
                        ref={el => otpRefs.current[index] = el}
                        type="text"
                        inputMode="numeric"
                        maxLength={1}
                        value={digit}
                        onChange={(e) => handleOTPChange(index, e.target.value)}
                        onKeyDown={(e) => handleOTPKeyDown(index, e)}
                        className="relative w-12 h-14 sm:w-14 sm:h-16 md:w-16 md:h-18 text-center text-xl sm:text-2xl font-bold bg-[#2a2a2a] border border-gray-700 focus:border-[#FF8800] rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-[#FF8800]/20 transition-all duration-300 z-10 shadow-lg shadow-black/20"
                      />

                      {/* Animated bottom border */}

                      {/* Decorative elements */}
                    </motion.div>
                  ))}
                </div>
                {errors.otp && (
                  <motion.div
                    initial={{ opacity: 0, y: -5 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-3 text-sm text-red-400 flex items-center justify-center gap-2 p-2 bg-red-500/10 border border-red-500/20 rounded-lg"
                  >
                    <AlertCircle size={16} className="text-red-400" />
                    <span className="font-medium">{errors.otp}</span>
                  </motion.div>
                )}
              </div>

              <motion.button
                onClick={handleVerifyOTP}
                disabled={isLoading || otp.some(digit => !digit)}
                className="relative w-full group overflow-hidden"
                style={{ minHeight: '52px' }}
                whileHover={{ scale: otp.every(digit => digit) && !isLoading ? 1.02 : 1 }}
                whileTap={{ scale: otp.every(digit => digit) && !isLoading ? 0.98 : 1 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                {/* Enhanced button background with animated gradient */}
                <div className={`absolute inset-0 rounded-xl transition-all duration-500 ${
                  otp.every(digit => digit) && !isLoading 
                    ? 'bg-gradient-to-r from-orange-600 via-orange-500 to-orange-600 bg-size-200 group-hover:bg-pos-100 border border-orange-500/50' 
                    : 'bg-gradient-to-r from-gray-800 to-gray-900 border border-gray-700/30'
                }`} />

                {/* Animated light effect */}
                {otp.every(digit => digit) && !isLoading && (
                  <div className="absolute inset-0">
                    <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-orange-400/0 via-orange-400/30 to-orange-400/0 opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500"></div>
                    <div className="absolute -inset-1 bg-gradient-to-r from-orange-500/0 via-orange-500/10 to-orange-500/0 rounded-xl opacity-0 group-hover:opacity-100 blur-md group-hover:animate-pulse transition-opacity duration-500"></div>
                  </div>
                )}

                {/* Subtle pattern overlay */}
                <div className="absolute inset-0 rounded-xl bg-grid-white/[0.02] bg-[length:20px_20px] opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Button content with enhanced animation */}
                <div className="relative flex items-center justify-center gap-3 py-4 px-6 text-white font-semibold text-lg">
                  {isLoading ? (
                    <div className="flex items-center gap-3">
                      <Loader2 size={20} className="animate-spin text-orange-200" />
                      <span className="text-orange-100">Verifying...</span>
                    </div>
                  ) : (
                    <>
                      <span className="group-hover:translate-x-[-2px] transition-transform duration-300">Verify & Continue</span>
                      <CheckCircle
                        size={20}
                        className="group-hover:scale-125 group-hover:text-green-300 transition-all duration-300"
                      />
                    </>
                  )}
                </div>

                {/* Decorative elements */}

              </motion.button>

              <motion.div
                className="text-center mt-2"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1.4 }}
              >
                {resendTimer > 0 ? (
                  <div className="flex items-center justify-center gap-2 bg-gray-800/50 border border-gray-700/30 rounded-lg py-2 px-4">
                    <div className="w-4 h-4 relative">
                      <svg className="w-full h-full" viewBox="0 0 24 24">
                        <circle
                          className="text-gray-700"
                          strokeWidth="2"
                          stroke="currentColor"
                          fill="transparent"
                          r="10"
                          cx="12"
                          cy="12"
                        />
                        <circle
                          className="text-orange-500"
                          strokeWidth="2"
                          strokeDasharray={2 * Math.PI * 10}
                          strokeDashoffset={2 * Math.PI * 10 * (1 - resendTimer / 55)}
                          strokeLinecap="round"
                          stroke="currentColor"
                          fill="transparent"
                          r="10"
                          cx="12"
                          cy="12"
                        />
                      </svg>
                    </div>
                    <p className="text-gray-300 text-sm font-medium">
                      Resend code in <span className="text-orange-400 font-semibold">{resendTimer}s</span>
                    </p>
                  </div>
                ) : (
                  <motion.button
                    onClick={handleResendOTP}
                    disabled={isLoading}
                    className="relative group overflow-hidden bg-[#2a2a2a] hover:bg-[#333333] border border-gray-700 hover:border-[#FF8800]/30 rounded-lg py-2 px-6 transition-all duration-300"
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-orange-500/0 to-orange-400/0 group-hover:from-orange-500/10 group-hover:to-orange-400/10 transition-all duration-300"></div>
                    <div className="flex items-center gap-2">
                      <RotateCcw size={16} className="text-orange-400 group-hover:rotate-180 transition-transform duration-500" />
                      <span className="text-white text-sm font-semibold">Resend Verification Code</span>
                    </div>
                  </motion.button>
                )}
              </motion.div>
            </motion.div>

            {error && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 bg-red-500/10 border border-red-500/30 rounded-xl text-red-400 text-sm flex items-center gap-3 backdrop-blur-sm shadow-lg shadow-red-500/5"
              >
                <div className="p-2 bg-red-500/10 rounded-full">
                  <AlertCircle size={18} className="text-red-400" />
                </div>
                <div>
                  <span className="font-semibold block">Verification Failed</span>
                  <span className="text-red-300">{error}</span>
                </div>
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default OTPLogin;