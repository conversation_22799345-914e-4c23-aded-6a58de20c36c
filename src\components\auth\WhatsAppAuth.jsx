import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Phone, ArrowRight, Loader2, AlertCircle, CheckCircle, Mail } from 'lucide-react';
import { toast } from 'react-toastify';
import { useAuth } from '../../context/AuthContext';
import { Link, useLocation } from 'react-router-dom';

const WhatsAppAuth = ({ onSuccess }) => {
  // Use AuthContext state instead of local state
  const { otpSent, otpPhone, isLoading, sendOTP, verifyOTP, resendOTP, dispatch } = useAuth();
  const location = useLocation();

  // Form data
  const [formData, setFormData] = useState({
    phone: otpPhone || '', // Use phone from AuthContext if available
    otp: ['', '', '', '', '', '']
  });

  const [errors, setErrors] = useState({});

  // Resend timer state
  const [resendTimer, setResendTimer] = useState(0);
  const [canResend, setCanResend] = useState(true);

  // Timer effect for resend functionality
  useEffect(() => {
    let interval = null;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer(timer => {
          if (timer <= 1) {
            setCanResend(true);
            return 0;
          }
          return timer - 1;
        });
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [resendTimer]);

  // Effect to start timer when OTP is sent (handles page refresh/component mount)
  useEffect(() => {
    if (otpSent && canResend && resendTimer === 0) {
      // Start the 55-second timer when OTP is already sent
      setCanResend(false);
      setResendTimer(55);
    }
  }, [otpSent, canResend, resendTimer]);

  // Function to go back to phone input
  const goBackToPhone = () => {
    // Clear OTP state in AuthContext
    dispatch({
      type: 'SEND_OTP_FAILURE',
      payload: { error: null }
    });
    // Clear form data
    setFormData({
      phone: '',
      otp: ['', '', '', '', '', '']
    });
    setErrors({});
    // Reset timer state
    setResendTimer(0);
    setCanResend(true);
  };

  // Validation functions
  const validatePhone = (phone) => {
    const phoneRegex = /^[6-9]\d{9}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  // Handle input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle OTP input
  const handleOTPChange = (index, value) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newOTP = [...formData.otp];
      newOTP[index] = value;
      setFormData(prev => ({ ...prev, otp: newOTP }));

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`otp-${index + 1}`);
        if (nextInput) nextInput.focus();
      }
    }
  };

  // Handle OTP key navigation
  const handleOTPKeyDown = (index, e) => {
    if (e.key === 'Backspace' && !formData.otp[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`);
      if (prevInput) {
        prevInput.focus();
        const newOTP = [...formData.otp];
        newOTP[index - 1] = '';
        setFormData(prev => ({ ...prev, otp: newOTP }));
      }
    }
  };

  // WhatsApp OTP Send
  const handleSendWhatsAppOTP = async () => {
    if (!formData.phone) {
      setErrors({ phone: 'Phone number is required' });
      return;
    }

    if (!validatePhone(formData.phone)) {
      setErrors({ phone: 'Invalid phone number' });
      return;
    }

    setErrors({});

    try {
      const result = await sendOTP(formData.phone);

      if (result.success) {
        // Start the 55-second timer for resend
        setCanResend(false);
        setResendTimer(55);

        toast.success('OTP sent to your WhatsApp!', {
          position: "top-right",
          autoClose: 3000,
          theme: "dark"
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toast.error(error.message || 'Failed to send OTP', {
        position: "top-right",
        autoClose: 5000,
        theme: "dark"
      });
      setErrors({ phone: error.message });
    }
  };

  // WhatsApp OTP Verify
  const handleVerifyWhatsAppOTP = async () => {
    const otpString = formData.otp.join('');
    if (otpString.length !== 6) {
      setErrors({ otp: 'Please enter complete OTP' });
      return;
    }

    setErrors({});

    try {
      const result = await verifyOTP(otpPhone || formData.phone, otpString);

      if (result.success) {
        toast.success('Login successful!', {
          position: "top-right",
          autoClose: 3000,
          theme: "dark"
        });
        onSuccess(result.data);
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toast.error(error.message || 'Invalid OTP', {
        position: "top-right",
        autoClose: 5000,
        theme: "dark"
      });
      setErrors({ otp: error.message });
    }
  };

  // Handle resend OTP
  const handleResendOTP = async () => {
    if (!canResend || isLoading) {
      return;
    }

    try {
      const result = await resendOTP(otpPhone || formData.phone);

      if (result.success) {
        // Start the 55-second timer for resend
        setCanResend(false);
        setResendTimer(55);

        toast.success('OTP resent to your WhatsApp!', {
          position: "top-right",
          autoClose: 3000,
          theme: "dark"
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toast.error(error.message || 'Failed to resend OTP', {
        position: "top-right",
        autoClose: 5000,
        theme: "dark"
      });
    }
  };

  const renderPhoneInput = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-6 w-full"
    >
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">WhatsApp Login</h2>
        <p className="text-gray-400">Enter your phone number to receive OTP on WhatsApp</p>
      </div>

      {/* Error Message */}
      {errors.phone && (
        <div className="flex items-center gap-2 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
          <AlertCircle size={16} className="text-red-400" />
          <span className="text-red-400 text-sm">{errors.phone}</span>
        </div>
      )}

      {/* Phone Input */}
      <div>
        <div className="relative">
          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="tel"
            inputMode="numeric"
            placeholder="Enter 10-digit mobile number"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value.replace(/\D/g, '').slice(0, 10))}
            className={`w-full pl-12 pr-4 py-4 bg-black border-2 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 transition-all duration-200 ${
              errors.phone ? 'border-red-500' : 'border-gray-600'
            }`}
          />
        </div>
        <p className="text-gray-500 text-xs mt-1">We'll send a 6-digit OTP to your WhatsApp</p>
      </div>

      {/* Send OTP Button */}
      <button
        onClick={handleSendWhatsAppOTP}
        disabled={isLoading || !formData.phone}
        className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 rounded-lg font-medium hover:from-green-600 hover:to-green-700 transition-all duration-200 flex items-center justify-center gap-2 disabled:opacity-50"
      >
        {isLoading ? (
          <Loader2 className="animate-spin" size={20} />
        ) : (
          <>
            Send OTP on WhatsApp
            <ArrowRight size={16} />
          </>
        )}
      </button>

      {/* OR Divider */}
      <div className="flex items-center my-6">
        <div className="flex-1 border-t border-gray-600"></div>
        <span className="px-4 text-gray-400 text-sm">OR</span>
        <div className="flex-1 border-t border-gray-600"></div>
      </div>

      {/* Email Login Button */}
      <Link
        to="/login"
        state={{ from: location.state?.from }}
        className="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white py-3 rounded-lg font-medium hover:from-gray-600 hover:to-gray-700 transition-all duration-200 flex items-center justify-center gap-2 border border-gray-600"
      >
        <Mail size={16} />
        Login with Email
      </Link>
    </motion.div>
  );

  const renderOTPInput = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-6 w-full"
    >
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-white mb-2">Enter OTP</h2>
        <p className="text-gray-400">
          We've sent a 6-digit code to your WhatsApp
          <br />
          <span className="text-white font-medium">+91 {formData.phone}</span>
        </p>
      </div>

      {/* Error Message */}
      {errors.otp && (
        <div className="flex items-center gap-2 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
          <AlertCircle size={16} className="text-red-400" />
          <span className="text-red-400 text-sm">{errors.otp}</span>
        </div>
      )}

      {/* OTP Input */}
      <div className="w-full overflow-hidden">
        <div className="flex justify-center gap-1 sm:gap-2 md:gap-3 px-1 sm:px-2 max-w-full">
          {formData.otp.map((digit, index) => (
            <input
              key={index}
              id={`otp-${index}`}
              type="text"
              inputMode="numeric"
              value={digit}
              onChange={(e) => handleOTPChange(index, e.target.value)}
              onKeyDown={(e) => handleOTPKeyDown(index, e)}
              className="w-9 h-11 xs:w-10 xs:h-12 sm:w-12 sm:h-14 md:w-14 md:h-16 text-center text-base xs:text-lg sm:text-xl md:text-2xl font-bold bg-black border-2 border-gray-600 rounded-lg sm:rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 flex-shrink-0 min-w-0"
              maxLength={1}
            />
          ))}
        </div>
      </div>

      {/* Verify Button */}
      <button
        onClick={handleVerifyWhatsAppOTP}
        disabled={isLoading || formData.otp.join('').length !== 6}
        className="w-full bg-gradient-to-r from-orange-500 to-orange-600 text-white py-3 rounded-lg font-medium hover:from-orange-600 hover:to-orange-700 transition-all duration-200 flex items-center justify-center gap-2 disabled:opacity-50"
      >
        {isLoading ? (
          <Loader2 className="animate-spin" size={20} />
        ) : (
          <>
            Verify OTP
            <CheckCircle size={16} />
          </>
        )}
      </button>

      {/* Resend and Back */}
      <div className="text-center space-y-2">
        <button
          onClick={handleResendOTP}
          disabled={isLoading || !canResend}
          className={`text-sm transition-colors duration-200 ${
            canResend && !isLoading
              ? 'text-orange-400 hover:text-orange-300 cursor-pointer'
              : 'text-gray-500 cursor-not-allowed'
          }`}
        >
          {!canResend ? `Resend OTP in ${resendTimer}s` : 'Resend OTP'}
        </button>
        <p className="text-gray-400 text-sm">
          Wrong number?{' '}
          <button
            onClick={goBackToPhone}
            className="text-orange-400 hover:text-orange-300"
          >
            Change Number
          </button>
        </p>
      </div>
    </motion.div>
  );

  return (
    <div>
      {!otpSent ? renderPhoneInput() : renderOTPInput()}
    </div>
  );
};

export default WhatsAppAuth;
