import { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import {
  ShoppingBag,
  ChevronLeft,
  Minus,
  Plus,
  Trash2,
  CreditCard,
  ShieldCheck,
  Truck,
  CheckCircle,
  Mail
} from 'lucide-react';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';
import { toast } from 'react-toastify';
// No need for AddressForm as we use the profile's address

// Global image cache for instant loading across cart page
const cartImageCache = new Map();
const cartPreloadedImages = new Set();

// Enhanced image optimization utility for cart page
const optimizeCartImageUrl = (url, width = 800, height = 800, quality = 'good') => {
  if (!url) return 'https://via.placeholder.com/400x400/1a1a1a/ffffff?text=Image+Not+Found';

  const cacheKey = `${url}-${width}-${height}-${quality}`;

  if (cartImageCache.has(cacheKey)) {
    return cartImageCache.get(cacheKey);
  }

  let optimizedUrl;

  if (url.includes('cloudinary.com')) {
    optimizedUrl = url.replace('/upload/', `/upload/w_${width},h_${height},c_limit,f_webp,q_auto:${quality},fl_progressive,dpr_auto,fl_immutable_cache,fl_awebp/`);
  } else if (url.includes('unsplash.com')) {
    optimizedUrl = `${url}&w=${width}&h=${height}&fit=crop&auto=format&q=85`;
  } else {
    optimizedUrl = url;
  }

  cartImageCache.set(cacheKey, optimizedUrl);
  return optimizedUrl;
};

// SUPER AGGRESSIVE image preloading function for cart page
const preloadCartImages = (items) => {
  if (!items || items.length === 0) return;

  const preloadPromises = [];

  items.forEach((item, index) => {
    // Preload ALL cart item images for instant experience
    if (item.image) {
      const optimizedUrl = optimizeCartImageUrl(item.image, 800, 1000, 'good');
      if (!cartPreloadedImages.has(optimizedUrl)) {
        cartPreloadedImages.add(optimizedUrl);
        preloadPromises.push(new Promise((resolve) => {
          const img = new Image();
          img.onload = () => resolve();
          img.onerror = () => resolve();
          img.src = optimizedUrl;
          // Enable faster loading with fetch priority
          if ('fetchPriority' in img) {
            img.fetchPriority = 'high';
          }
          if ('decoding' in img) {
            img.decoding = 'async';
          }
          if ('loading' in img) {
            img.loading = 'eager';
          }
        }));
      }
    }

    // Preload additional images if available
    if (item.images && item.images.length > 0) {
      item.images.forEach(imageUrl => {
        const optimizedUrl = optimizeCartImageUrl(imageUrl, 800, 1000, 'good');
        if (!cartPreloadedImages.has(optimizedUrl)) {
          cartPreloadedImages.add(optimizedUrl);
          preloadPromises.push(new Promise((resolve) => {
            const img = new Image();
            img.onload = () => resolve();
            img.onerror = () => resolve();
            img.src = optimizedUrl;
          }));
        }
      });
    }
  });

  // Wait for critical images to load
  return Promise.all(preloadPromises);
};

function CartPage() {
  const navigate = useNavigate();
  const { items, removeFromCart, updateQuantity, clearCart, subtotal, loading, migrating, refreshCart } = useCart();
  const { user, isAuthenticated, refreshProfile } = useAuth();
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);
  const [animateTotal, setAnimateTotal] = useState(false);
  const [orderConfirmed, setOrderConfirmed] = useState(false);
  const [orderDetails, setOrderDetails] = useState(null);
  const [imagesPreloaded, setImagesPreloaded] = useState(false);
  const [razorpayLoaded, setRazorpayLoaded] = useState(false);
  const [autoCheckout, setAutoCheckout] = useState(false);
  const location = useLocation();
  // No need for address form state variables as we use the profile's address

  // Load Razorpay script
  useEffect(() => {
    const loadRazorpayScript = () => {
      return new Promise((resolve) => {
        if (window.Razorpay) {
          setRazorpayLoaded(true);
          resolve(true);
          return;
        }

        const script = document.createElement('script');
        script.src = 'https://checkout.razorpay.com/v1/checkout.js';
        script.onload = () => {
          setRazorpayLoaded(true);
          resolve(true);
        };
        script.onerror = () => {
          console.error('Failed to load Razorpay script');
          toast.error('Failed to load payment gateway');
          resolve(false);
        };
        document.body.appendChild(script);
      });
    };

    loadRazorpayScript();
  }, []);

  // SUPER AGGRESSIVE preloading for cart items
  useEffect(() => {
    if (items.length === 0) return;

    const preloadAllImages = async () => {
      try {
        // Preload all cart item images
        await preloadCartImages(items);
        setImagesPreloaded(true);
      } catch (error) {
        console.error('Failed to preload cart images:', error);
        setImagesPreloaded(true); // Continue even if preloading fails
      }
    };

    preloadAllImages();
  }, [items]);

  // Helper to extract a usable shipping address from the user object
  const extractShippingAddress = (user) => {
    if (!user) return null;
    // 1. Direct shipping_address field
    if (user.shipping_address && Object.keys(user.shipping_address).length > 0) {
      return {
        line1: user.shipping_address.line1 || user.shipping_address.street || user.shipping_address.address_line_1 || '',
        line2: user.shipping_address.line2 || user.shipping_address.address_line_2 || '',
        city: user.shipping_address.city || '',
        state: user.shipping_address.state || '',
        postal_code: user.shipping_address.postal_code || user.shipping_address.zip || '',
        country: user.shipping_address.country || 'India'
      };
    }
    // 2. Fallback to addresses array
    if (Array.isArray(user.addresses)) {
      const found = user.addresses.find(
        (addr) => addr.type === 'shipping' || addr.type === 'both' || addr.is_default
      );
      if (found && Object.keys(found).length > 0) {
        return {
          line1: found.address_line_1 || found.line1 || found.street || '',
          line2: found.address_line_2 || found.line2 || '',
          city: found.city || '',
          state: found.state || '',
          postal_code: found.postal_code || found.zip || '',
          country: found.country || 'India'
        };
      }
    }
    return null;
  };

  // Email validation function
  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };
  
  // Handle address form submission
  // No need for address form handling as we use the profile's address

  useEffect(() => {
    setAnimateTotal(true);
    const timer = setTimeout(() => setAnimateTotal(false), 500);
    return () => clearTimeout(timer);
  }, [subtotal]);

  // Handle auto-checkout when returning from login or address form
  useEffect(() => {
    const shouldAutoCheckout = (location.state?.fromLogin || location.state?.fromAddressForm) && location.state?.proceedToCheckout;
    if (shouldAutoCheckout && isAuthenticated && !migrating && !isCheckingOut && !orderConfirmed) {
      console.log('🔄 Auto-checkout triggered after', location.state?.fromLogin ? 'login' : 'address form');
      console.log('🔄 Auto-checkout state:', { isAuthenticated, hasUser: !!user, userID: user?.id, itemsLength: items.length });

      // If coming from address form, wait for user context to stabilize then refresh cart
      if (location.state?.fromAddressForm) {
        console.log('🔄 Coming from address form, waiting for user context to stabilize...');

        // Wait for user context to be available again
        const waitForUserAndRefresh = async () => {
          let attempts = 0;
          const maxAttempts = 20; // 2 seconds max wait

          // Wait for user to be available
          while (!user?.id && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
          }

          if (!user?.id) {
            console.error('❌ User context not available after waiting');
            toast.error('Please refresh the page and try again.');
            return;
          }

          console.log('✅ User context available, refreshing cart...');
          try {
            await refreshCart();
            // Check cart again after refresh
            if (items.length > 0) {
              setTimeout(() => {
                handleCheckout();
              }, 300);
            } else {
              console.error('❌ Cart is still empty after refresh');
              // Only show error if order is not confirmed
              if (!orderConfirmed) {
                toast.error('Your cart appears to be empty. Please add items and try again.');
              }
            }
          } catch (error) {
            console.error('❌ Failed to refresh cart:', error);
            // Only show error if order is not confirmed
            if (!orderConfirmed) {
              toast.error('Failed to load cart. Please refresh the page.');
            }
          }
        };

        waitForUserAndRefresh();
      } else if (items.length > 0) {
        // Coming from login, proceed normally
        setTimeout(() => {
          handleCheckout();
        }, 500);
      }
    }
  }, [location.state, isAuthenticated, items.length, migrating, isCheckingOut, orderConfirmed]);

  const handleCheckout = async () => {
    // Prevent checkout if order is already confirmed
    if (orderConfirmed) {
      console.log('🛒 Order already confirmed, skipping checkout');
      return;
    }

    console.log('🛒 handleCheckout - Starting checkout process');
    console.log('🛒 Auth state:', { isAuthenticated, user: user?.email, userId: user?.id });

    // 1. User Authentication Check - Deferred Authentication Flow
    // Check if we have valid tokens even if user object is temporarily undefined
    const storedTokens = localStorage.getItem('wolffoxx_tokens');
    const hasValidTokens = storedTokens && JSON.parse(storedTokens)?.accessToken;

    if (!isAuthenticated || (!user && !hasValidTokens)) {
      console.log('🛒 User not authenticated - redirecting to login with cart preservation');

      // Preserve cart state in localStorage for guest users (already handled by CartContext)
      // The cart items are automatically saved to localStorage by the CartContext

      toast.info('Please log in to complete your order. Your cart will be preserved.', {
        position: "top-right",
        autoClose: 4000,
        theme: "dark"
      });

      // Navigate to login with checkout flow state for smart routing
      navigate('/login', {
        state: {
          returnTo: '/cart',
          fromCheckout: true,
          message: 'Please log in to complete your order'
        }
      });
      return;
    }

    // If user is temporarily undefined but we have tokens, try to refresh profile
    if (!user && hasValidTokens) {
      console.log('🔄 User temporarily undefined, refreshing profile...');
      try {
        await refreshProfile();
        // Wait a bit for the profile to update
        await new Promise(resolve => setTimeout(resolve, 500));

        // Check if user is now available
        if (!user) {
          console.error('❌ User still undefined after profile refresh');
          toast.error('Please refresh the page and try again.');
          return;
        }
      } catch (error) {
        console.error('❌ Failed to refresh profile:', error);
        toast.error('Please refresh the page and try again.');
        return;
      }
    }

    // 1.1. Wait for cart migration to complete if in progress
    if (migrating) {
      console.log('🔄 Cart migration in progress, waiting for completion...');
      toast.info('Preparing your cart...', {
        position: "top-right",
        autoClose: 2000,
        theme: "dark"
      });

      // Wait for migration to complete (with timeout)
      let attempts = 0;
      const maxAttempts = 50; // 5 seconds max wait
      while (migrating && attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }

      if (migrating) {
        console.error('🛒 Cart migration timeout - proceeding anyway');
        toast.warning('Cart preparation taking longer than expected. Proceeding...', {
          position: "top-right",
          autoClose: 3000,
          theme: "dark"
        });
      } else {
        console.log('✅ Cart migration completed, proceeding with checkout');
      }
    }

    // 1.5. Small delay to ensure cart state is fully updated after migration
    if (!migrating) {
      console.log('🛒 Allowing cart state to stabilize...');
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // 1.6. Refresh user profile to get latest data
    console.log('🛒 Refreshing user profile...');
    try {
      const profileRefresh = await refreshProfile();
      if (profileRefresh.success) {
        console.log('🛒 Profile refreshed successfully');
        // Small delay to ensure state is updated
        await new Promise(resolve => setTimeout(resolve, 100));
      } else {
        console.warn('🛒 Profile refresh failed, continuing with cached data');
      }
    } catch (error) {
      console.warn('🛒 Profile refresh error, continuing with cached data:', error);
    }

    // 2. Always redirect to checkout/address page for payment method selection
    console.log('🛒 Redirecting to checkout/address page for payment method selection');
    toast.info('Please complete your address and select payment method');
    navigate('/checkout/address');
    return;

    // 2. Cart Validation
    console.log('🛒 Cart items:', items);
    console.log('🛒 Cart subtotal:', subtotal);

    if (items.length === 0) {
      // Don't show error if order is already confirmed (cart was cleared after successful order)
      if (orderConfirmed) {
        console.log('🛒 Cart is empty because order was confirmed, skipping validation');
        return;
      }

      // Check if we might be in a race condition - try refreshing cart once
      console.log('🛒 Cart appears empty, attempting to refresh...');
      try {
        await refreshCart();
        // Check again after refresh
        if (items.length === 0) {
          console.error('🛒 Cart is still empty after refresh');
          // Only show error if order is not confirmed
          if (!orderConfirmed) {
            toast.error('Your cart appears to be empty. Please add items and try again.');
          }
          return;
        }
        console.log('✅ Cart refreshed successfully, proceeding with checkout');
      } catch (error) {
        console.error('❌ Failed to refresh cart:', error);
        // Only show error if order is not confirmed
        if (!orderConfirmed) {
          toast.error('Your cart appears to be empty. Please add items and try again.');
        }
        return;
      }
    }

    // 3. Redirect to checkout/address page for payment method selection
    console.log('🛒 Redirecting to checkout/address page for payment method selection');
    toast.info('Please complete your address and select payment method');
    navigate('/checkout/address');
    return;
  };

  const handleRemoveItem = (itemId, color, size) => {
    removeFromCart(itemId, color, size);
    setShowDeleteConfirm(null);
  };

  const handleQuantityDecrease = (item) => {
    if (item.quantity > 1) {
      updateQuantity(item.id, item.quantity - 1, item.color, item.size);
    }
  };

  const handleQuantityIncrease = (item) => {
    updateQuantity(item.id, item.quantity + 1, item.color, item.size);
  };



  // Prices are inclusive of taxes
  const total = subtotal;

  // Order Confirmation Component
  if (orderConfirmed && orderDetails) {
    // Calculate estimated delivery date (7 days from now)
    const estimatedDelivery = new Date();
    estimatedDelivery.setDate(estimatedDelivery.getDate() + 7);
    
    return (
      <div className="min-h-screen bg-[#000000]">
        <div className="container mx-auto px-4 py-6 max-w-4xl">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-6">
              <div className="bg-green-500 p-6 rounded-full">
                <CheckCircle size={48} className="text-white" />
              </div>
            </div>
            <h1 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Order Confirmed!
            </h1>
            <p className="text-[#AAAAAA] text-lg mb-2">
              Thank you for your order, {orderDetails.customer_name}
            </p>
            <p className="text-[#AAAAAA] mb-6">
              Order #{orderDetails.order_number}
            </p>
          </div>

          <div className="bg-[#1a1a1a] rounded-xl p-6 border border-[#404040] mb-6">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
              <Mail size={20} />
              Confirmation Email Sent
            </h2>
            <p className="text-[#AAAAAA] mb-4">
              A confirmation email has been sent to <span className="text-white font-medium">{orderDetails.customer_email}</span> with your order details and tracking information.
            </p>
            
            {/* Order Summary */}
            <div className="bg-[#2a2a2a] p-4 rounded-lg mb-4">
              <h3 className="text-white font-medium mb-3">Order Summary</h3>
              <div className="space-y-2">
                {orderDetails.items.map((item, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span className="text-[#AAAAAA]">
                      {item.name} ({item.color}, {item.size}) x {item.quantity}
                    </span>
                    <span className="text-white">₹{item.total.toFixed(2)}</span>
                  </div>
                ))}
                <div className="border-t border-[#404040] pt-2 mt-2 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-[#AAAAAA]">Subtotal</span>
                    <span className="text-white">₹{orderDetails.subtotal.toFixed(2)}</span>
                  </div>

                  <div className="flex justify-between text-sm">
                    <span className="text-[#AAAAAA]">Shipping</span>
                    <span className="text-white">{orderDetails.shipping_amount > 0 ? `₹${orderDetails.shipping_amount.toFixed(2)}` : 'Free'}</span>
                  </div>
                  <div className="border-t border-[#404040] pt-2">
                    <div className="flex justify-between font-semibold">
                      <span className="text-white">Total</span>
                      <span className="text-white">₹{orderDetails.total_amount.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Shipping Information */}
            {orderDetails.shipping_address && (
              <div className="bg-[#2a2a2a] p-4 rounded-lg mb-4">
                <h3 className="text-white font-medium mb-3 flex items-center gap-2">
                  <Truck size={16} className="text-amber-500" />
                  Shipping Information
                </h3>
                <div className="text-sm text-[#AAAAAA]">
                  <p className="text-white font-medium">{orderDetails.customer_name}</p>
                  <p>{orderDetails.shipping_address.line1 || orderDetails.shipping_address.street}</p>
                  {orderDetails.shipping_address.line2 && <p>{orderDetails.shipping_address.line2}</p>}
                  <p>
                    {orderDetails.shipping_address.city}, {orderDetails.shipping_address.state} {orderDetails.shipping_address.postal_code || orderDetails.shipping_address.zip}
                  </p>
                  <p>{orderDetails.shipping_address.country}</p>
                </div>
              </div>
            )}
            
            {/* Estimated Delivery */}
            <div className="bg-[#2a2a2a] p-4 rounded-lg">
              <h3 className="text-white font-medium mb-3 flex items-center gap-2">
                <Truck size={16} className="text-emerald-500" />
                Estimated Delivery
              </h3>
              <p className="text-[#AAAAAA] text-sm">
                Your order should arrive by <span className="text-white font-medium">
                  {estimatedDelivery.toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </span>
              </p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => navigate('/orders')}
              className="px-6 py-3 bg-gradient-to-br from-[#FF6B35] to-[#F7931E] text-white font-medium rounded-lg hover:from-[#1a3f9e] hover:to-[#4a9dd4] transition-all duration-300"
            >
              View Order History
            </button>
            <button
              onClick={() => navigate('/collections')}
              className="px-6 py-3 bg-[#2a2a2a] text-white font-medium rounded-lg hover:bg-[#404040] transition-colors border border-[#404040]"
            >
              Continue Shopping
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#000000]">
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Mobile Header */}
        <div className="mb-6 flex items-center justify-between">
          <button
            onClick={() => navigate(-1)}
            className="text-[#AAAAAA] hover:text-white flex items-center gap-2 transition-all duration-300 group"
          >
            <div className="bg-[#2a2a2a] p-2 rounded-full group-hover:bg-[#404040] transition-colors">
              <ChevronLeft size={16} />
            </div>
            <span className="font-medium hidden sm:inline">Continue Shopping</span>
          </button>
          {/* Mobile trust badges */}
          <div className="flex items-center gap-2 sm:gap-4">
            <div className="flex items-center gap-1 text-[#AAAAAA] text-xs sm:text-sm">
              <ShieldCheck size={16} className="text-emerald-500" />
              <span className="hidden sm:inline">Secure</span>
            </div>
            <div className="flex items-center gap-1 text-[#AAAAAA] text-xs sm:text-sm">
              <Truck size={16} className="text-amber-500" />
              <span className="hidden sm:inline">Free Ship</span>
            </div>
          </div>
        </div>

        {/* Page Title */}
        <div className="mb-8 border-b border-[#2a2a2a] pb-6">
          <h1 className="text-3xl sm:text-4xl font-bold text-white mb-2 tracking-tighter">
            Your <span className="text-[#AAAAAA]">Cart</span>
          </h1>
          <p className="text-[#AAAAAA] text-sm sm:text-base">
            {items.length} {items.length === 1 ? 'item' : 'items'} in your shopping bag
          </p>
        </div>

        {migrating ? (
          <div className="text-center py-16 bg-[#1a1a1a] rounded-xl border border-[#404040] backdrop-blur-sm shadow-xl">
            <div className="flex justify-center mb-6">
              <div className="bg-[#2a2a2a] p-6 rounded-full">
                <div className="w-8 h-8 border-2 border-[#FF6F35] border-t-transparent rounded-full animate-spin" />
              </div>
            </div>
            <h2 className="text-xl sm:text-2xl font-bold text-white mb-4">
              Preparing Your Cart
            </h2>
            <p className="text-[#AAAAAA] text-sm sm:text-base max-w-md mx-auto">
              We're migrating your cart items. This will just take a moment...
            </p>
          </div>
        ) : items.length === 0 ? (
          <div className="text-center py-16 bg-[#1a1a1a] rounded-xl border border-[#404040] backdrop-blur-sm shadow-xl">
            <div className="flex justify-center mb-6">
              <div className="bg-[#2a2a2a] p-6 rounded-full">
                <ShoppingBag size={40} className="text-[#AAAAAA]" />
              </div>
            </div>
            <h2 className="text-xl sm:text-2xl text-white mb-4 font-bold">Your cart is empty</h2>
            <p className="text-[#AAAAAA] mb-8 max-w-md mx-auto text-sm sm:text-base px-4">
              Looks like you haven't added anything to your cart yet. Discover our premium selection and find something amazing.
            </p>
            <button
              onClick={() => navigate('/collections')}
              className="px-8 py-3 bg-gradient-to-br from-[#FF6B35] to-[#F7931E]  text-white font-medium rounded-lg transition-all duration-300 shadow-lg"
            >
              Start Shopping
            </button>
          </div>
        ) : (
          <div className="space-y-6 lg:grid lg:grid-cols-12 lg:gap-8 lg:space-y-0">
            {/* Cart Items - Mobile First */}
            <div className="lg:col-span-8">
              <div className="bg-[#1a1a1a] backdrop-blur-md rounded-xl overflow-hidden shadow-xl border border-[#404040]">
                {/* Desktop Header - Hidden on Mobile */}
                <div className="hidden md:block p-6 border-b border-[#2a2a2a]">
                  <div className="flex justify-between text-[#AAAAAA] text-sm font-medium">
                    <span className="uppercase tracking-wider">Product</span>
                    <div className="grid grid-cols-4 gap-3 w-[320px]">
                      <span className="text-center uppercase tracking-wider">Price</span>
                      <span className="text-center uppercase tracking-wider">Qty</span>
                      <span className="text-center uppercase tracking-wider">Total</span>
                      {/* <span className="text-center uppercase tracking-wider">Remove</span> */}
                    </div>
                  </div>
                </div>

                {/* Cart Items */}
                {items.map((item) => (
                  <div
                    key={`${item.id}-${item.color}-${item.size}`}
                    className="p-3 sm:p-6 border-b border-[#2a2a2a] hover:bg-[#2a2a2a] transition-colors duration-300"
                  >
                    {/* Mobile Layout */}
                    <div className="md:hidden">
                      <div className="flex gap-3 mb-4">
                        <Link
                          to={`/product/${item.id}`}
                          className="relative overflow-hidden rounded-lg w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-gray-800 to-gray-900 shadow-lg flex-shrink-0 hover:scale-105 transition-transform duration-300"
                        >
                          <img
                            src={optimizeCartImageUrl(item.image, 800, 1000, 'good')}
                            alt={item.name}
                            className="w-full h-full object-cover"
                            loading="eager"
                            fetchPriority="high"
                            onError={(e) => {
                              console.log('Cart image failed to load:', e.target.src);
                              e.target.src = 'https://via.placeholder.com/400x400/1a1a1a/ffffff?text=Image+Not+Found';
                            }}
                          />
                        </Link>
                        <div className="flex-1 min-w-0 pr-3">
                          <Link to={`/product/${item.id}`}>
                            <h3 className="text-white font-medium text-sm sm:text-base mb-2 hover:text-cyan-400 transition-colors leading-tight">
                              {item.name.length > 40 ? `${item.name.substring(0, 40)}...` : item.name}
                            </h3>
                          </Link>
                          <div className="text-[#AAAAAA] text-sm space-y-1">
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full border border-[#404040]"
                                style={{
                                  backgroundColor: !item.color || item.color === 'Default' ? '#6b7280' : (item.color?.toLowerCase?.()?.replace(' ', '') || '#6b7280'),
                                  opacity: !item.color || item.color === 'Default' ? 0.5 : 1
                                }}
                              ></div>
                              <span>{item.color}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 rounded-sm border border-[#404040] flex items-center justify-center text-[9px]">
                                {item.size}
                              </div>
                              <span>Size: {item.size}</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right flex-shrink-0 min-w-[100px] flex flex-col items-end">
                          <div className="text-white font-medium text-base sm:text-lg mb-3">
                            ₹{(parseFloat(item.salePrice || item.price) || 0).toFixed(2)}
                          </div>
                          {showDeleteConfirm === `${item.id}-${item.color}-${item.size}` ? (
                            <div className="flex items-center gap-1">
                              <button
                                className="bg-red-600 text-white p-2 rounded-md hover:bg-red-700 transition-colors active:scale-95"
                                onClick={() => handleRemoveItem(item.id, item.color, item.size)}
                              >
                                <Trash2 size={14} />
                              </button>
                              <button
                                className="bg-[#404040] text-white p-2 rounded-md hover:bg-[#6a6a6a] transition-colors active:scale-95"
                                onClick={() => setShowDeleteConfirm(null)}
                              >
                                <ChevronLeft size={14} />
                              </button>
                            </div>
                          ) : (
                            <button
                              className="text-red-400 hover:text-red-500 transition-colors p-2 hover:scale-110 active:scale-95 bg-[#2a2a2a] rounded-md"
                              onClick={() => setShowDeleteConfirm(`${item.id}-${item.color}-${item.size}`)}
                            >
                              <Trash2 size={16} />
                            </button>
                          )}
                        </div>
                      </div>
                      {/* Mobile Quantity Controls */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <button
                            className="w-10 h-10 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-l-md transition-colors active:scale-95"
                            onClick={() => handleQuantityDecrease(item)}
                            disabled={item.quantity <= 1}
                          >
                            <Minus size={16} />
                          </button>
                          <div className="w-12 h-10 bg-[#2a2a2a] flex items-center justify-center text-white font-medium">
                            {item.quantity}
                          </div>
                          <button
                            className="w-10 h-10 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-r-md transition-colors active:scale-95"
                            onClick={() => handleQuantityIncrease(item)}
                          >
                            <Plus size={16} />
                          </button>
                        </div>
                        <div className="text-white font-semibold text-lg">
                          ₹{((parseFloat(item.salePrice || item.price) || 0) * item.quantity).toFixed(2)}
                        </div>
                      </div>
                    </div>

                    {/* Desktop Layout */}
                    <div className="hidden md:flex md:items-center justify-between">
                      <div className="flex items-center gap-6">
                        <Link
                          to={`/product/${item.id}`}
                          className="relative overflow-hidden rounded-lg w-24 h-24 bg-gradient-to-br from-gray-800 to-gray-900 shadow-lg hover:scale-105 transition-transform duration-300"
                        >
                          <img
                            src={optimizeCartImageUrl(item.image, 800, 1000, 'good')}
                            alt={item.name}
                            className="w-full h-full object-cover"
                            loading="eager"
                            fetchPriority="high"
                            onError={(e) => {
                              console.log('Cart image failed to load:', e.target.src);
                              e.target.src = 'https://via.placeholder.com/400x400/1a1a1a/ffffff?text=Image+Not+Found';
                            }}
                          />
                        </Link>
                        <div className="min-w-0 flex-1">
                          <Link to={`/product/${item.id}`}>
                            <h3 className="text-white hover:text-cyan-400 font-medium text-lg transition-colors duration-300 cursor-pointer leading-tight">
                              {item.name.length > 30 ? `${item.name.substring(0, 30)}...` : item.name}
                            </h3>
                          </Link>
                          <div className="text-[#AAAAAA] text-sm mt-2 space-y-1">
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full border border-[#404040]"
                                style={{
                                  backgroundColor: !item.color || item.color === 'Default' ? '#6b7280' : (item.color?.toLowerCase?.()?.replace(' ', '') || '#6b7280'),
                                  opacity: !item.color || item.color === 'Default' ? 0.5 : 1
                                }}
                              ></div>
                              <span>{item.color}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 rounded-sm border border-[#404040] flex items-center justify-center text-[9px]">
                                {item.size}
                              </div>
                              <span>Size: {item.size}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-4 gap-3 items-center w-[320px]">
                        <div className="text-white text-center font-medium">
                          ₹{(parseFloat(item.salePrice || item.price) || 0).toFixed(2)}
                        </div>
                        <div className="flex items-center justify-center">
                          <button
                            className="w-7 h-7 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-l-md transition-colors active:scale-95"
                            onClick={() => handleQuantityDecrease(item)}
                            disabled={item.quantity <= 1}
                          >
                            <Minus size={12} />
                          </button>
                          <div className="w-8 h-7 bg-[#2a2a2a] flex items-center justify-center text-white font-medium text-sm">
                            {item.quantity}
                          </div>
                          <button
                            className="w-7 h-7 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-r-md transition-colors active:scale-95"
                            onClick={() => handleQuantityIncrease(item)}
                          >
                            <Plus size={12} />
                          </button>
                        </div>
                        <div className="text-white font-medium text-center">
                          ₹{((parseFloat(item.salePrice || item.price) || 0) * item.quantity).toFixed(2)}
                        </div>
                        <div className="flex justify-center">
                          {showDeleteConfirm === `${item.id}-${item.color}-${item.size}` ? (
                            <div className="flex items-center gap-1">
                              <button
                                className="bg-red-600 text-white p-1.5 rounded-md hover:bg-red-700 transition-colors"
                                onClick={() => handleRemoveItem(item.id, item.color, item.size)}
                              >
                                <Trash2 size={12} />
                              </button>
                              <button
                                className="bg-[#404040] text-white p-1.5 rounded-md hover:bg-[#6a6a6a] transition-colors"
                                onClick={() => setShowDeleteConfirm(null)}
                              >
                                <ChevronLeft size={12} />
                              </button>
                            </div>
                          ) : (
                            <button
                              className="text-[#AAAAAA] hover:text-red-500 transition-colors p-1.5 hover:scale-110 active:scale-95 bg-[#2a2a2a] rounded-md"
                              onClick={() => setShowDeleteConfirm(`${item.id}-${item.color}-${item.size}`)}
                            >
                              <Trash2 size={14} />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                <div className="p-4 sm:p-6 flex justify-end">
                  <button
                    onClick={clearCart}
                    className="text-[#AAAAAA] hover:text-red-500 text-sm flex items-center gap-1 transition-colors duration-300 p-2"
                  >
                    <Trash2 size={14} />
                    <span>Clear cart</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-4">
              <div className="bg-[#1a1a1a] backdrop-blur-md rounded-xl p-4 sm:p-6 border border-[#404040] shadow-xl lg:sticky lg:top-8">
                <h2 className="text-xl sm:text-2xl font-semibold text-white mb-6 sm:mb-8 border-b border-[#2a2a2a] pb-4">
                  Order Summary
                </h2>
                <div className="space-y-3 sm:space-y-4 mb-6">
                  <div className="flex justify-between">
                    <span className="text-[#AAAAAA]">Subtotal</span>
                    <span className="text-white font-medium">₹{subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-[#AAAAAA]">Shipping</span>
                    <span className="text-white font-medium">Free</span>
                  </div>

                </div>
                <div className="border-t border-[#2a2a2a] pt-4 sm:pt-6 mb-6">
                  <div className="flex justify-between text-lg sm:text-xl font-semibold">
                    <span className="text-white">Total</span>
                    <span
                      className={`text-white transition-all duration-500 ${
                        animateTotal ? 'scale-110 text-gray-300' : ''
                      }`}
                    >
                      ₹{total.toFixed(2)}
                    </span>
                  </div>
                </div>

                {/* Shipping Address Section */}
                <div className="mb-6 border-t border-[#2a2a2a] pt-4">
                  {/* <div className="flex justify-between items-center mb-3">
                    <h3 className="text-white font-medium">Shipping Address</h3>
                    <button 
                      onClick={() => navigate('/profile')}
                      className="text-cyan-400 hover:text-cyan-300 flex items-center gap-1 text-sm"
                    >
                      <Edit size={14} />
                      {user?.shipping_address && user.shipping_address.city ? 'Edit' : 'Add'}
                    </button>
                  </div> */}
                                    
                  {/* Note about billing address */}
                  <div className="mt-3 flex items-center">
                    <input
                      type="checkbox"
                      id="sameAsBilling"
                      checked={true}
                      disabled={true}
                      className="mr-2 h-4 w-4 accent-cyan-500"
                    />
                    <label htmlFor="sameAsBilling" className="text-[#AAAAAA] text-sm">
                      Using shipping address for billing
                    </label>
                  </div>
                </div>
                <button
                  className={`w-full py-4 bg-[#FF6F35]  text-white font-medium rounded-lg transition-all duration-300 flex items-center justify-center gap-2 shadow-lg mb-6 text-base sm:text-lg ${
                    (isCheckingOut || migrating) ? 'opacity-75 cursor-wait' : 'hover:scale-105 active:scale-95'
                  }`}
                  onClick={handleCheckout}
                  disabled={isCheckingOut || migrating}
                >
                  {migrating ? (
                    <>
                      <div className="w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin " />
                      Preparing Cart...
                    </>
                  ) : isCheckingOut ? (
                    <>
                      <div className="w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin " />
                      Processing...
                    </>
                  ) : (
                    <>
                      <CreditCard size={18}/>
                      Complete Order
                    </>
                  )}
                </button>



                {/* Payment Methods */}
<div className="grid grid-cols-3 gap-3">
  {/* Visa */}
<div className="bg-white rounded-lg p-3 flex items-center justify-center h-12 shadow-sm hover:shadow-md transition-shadow">
  <img
    src="https://static.cdnlogo.com/logos/v/34/visa.svg"
    alt="Visa"
    className="h-6 w-auto"
  />
</div>

  {/* Mastercard */}
  <div className="bg-white rounded-lg p-3 flex items-center justify-center h-12 shadow-sm hover:shadow-md transition-shadow">
    <img
      src="https://upload.wikimedia.org/wikipedia/commons/2/2a/Mastercard-logo.svg"
      alt="Mastercard"
      className="h-6"
    />
  </div>

  {/* PayPal */}
  <div className="bg-white rounded-lg p-3 flex items-center justify-center h-12 shadow-sm hover:shadow-md transition-shadow">
    <img
      src="https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg"
      alt="PayPal"
      className="h-6"
    />
  </div>
</div>
                {/* Trust Badges */}
                <div className="space-y-3 sm:space-y-4">
                  <div className="bg-[#2a2a2a] p-3 sm:p-4 rounded-lg flex gap-3 border border-[#404040]">
                    <ShieldCheck size={18} className="text-emerald-500 flex-shrink-0 mt-1" />
                    <p className="text-[#AAAAAA] text-sm">
                      <span className="font-medium">100% Secure Checkout</span>
                      <br />All transactions are secured and encrypted
                    </p>
                  </div>
                  <div className="bg-[#2a2a2a] p-3 sm:p-4 rounded-lg flex gap-3 border border-[#404040]">
                    <Truck size={18} className="text-amber-500 flex-shrink-0 mt-1" />
                    <p className="text-[#AAAAAA] text-sm">
                      <span className="font-medium">Free Shipping</span>
                      <br />On all orders!!
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default CartPage;