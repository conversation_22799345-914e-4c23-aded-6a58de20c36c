import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { useCart } from '../context/CartContext';
import { toast } from 'react-toastify';
import { motion } from 'framer-motion';
import { ArrowLeft, MapPin, ArrowRight, Package } from 'lucide-react';
import { optimizeImageUrl } from '../utils/imageOptimization';

const CheckoutAddressPage = () => {
  const { user, updateProfile, isLoading } = useAuth();
  const { items, subtotal, migrating, refreshCart } = useCart();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    shipping_address: {
      line1: '',
      line2: '',
      city: '',
      state: '',
      postal_code: '',
      country: 'India'
    }
  });

  const [fieldErrors, setFieldErrors] = useState({});
  const [isSaving, setIsSaving] = useState(false);
  const [hasCheckedCart, setHasCheckedCart] = useState(false);
  const [hasRefreshedCart, setHasRefreshedCart] = useState(false);


  // Calculate totals from cart items
  const calculateSubtotal = () => {
    // Use cart context subtotal if available, otherwise calculate manually
    if (subtotal && subtotal > 0) return subtotal;

    if (!items || items.length === 0) return 0;
    return items.reduce((sum, item) => {
      // Use sale price if available, otherwise use regular price (matching cart context logic)
      const effectivePrice = (item.is_sale === 1 && (item.sale_price || item.salePrice))
        ? parseFloat(item.sale_price || item.salePrice)
        : parseFloat(item.price) || 0;
      const quantity = parseInt(item.quantity) || 1;
      return sum + (effectivePrice * quantity);
    }, 0);
  };

  const calculateTotal = () => {
    // For now, total = subtotal (no additional fees like shipping, taxes, etc.)
    return calculateSubtotal();
  };

  // Initialize form with user data
  useEffect(() => {
    if (user) {
      console.log('DEBUG: User data for form initialization:', user);

      // Get shipping address from addresses array (default address or first address) - same logic as ProfilePage
      let shippingAddress = {};
      if (user.addresses && Array.isArray(user.addresses) && user.addresses.length > 0) {
        // Find default address or use first address
        const defaultAddress = user.addresses.find(addr => addr.is_default === 1 || addr.is_default === true);
        shippingAddress = defaultAddress || user.addresses[0];
        console.log('DEBUG: Found shipping address from addresses array:', shippingAddress);
      } else if (user.shipping_address) {
        // Fallback to shipping_address if addresses array not available
        shippingAddress = user.shipping_address;
        console.log('DEBUG: Using shipping_address fallback:', shippingAddress);
      }

      setFormData({
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email || '',
        phone: user.phone || '',
        shipping_address: {
          line1: shippingAddress.address_line_1 || shippingAddress.line1 || shippingAddress.street || '',
          line2: shippingAddress.address_line_2 || shippingAddress.line2 || '',
          city: shippingAddress.city || '',
          state: shippingAddress.state || '',
          postal_code: shippingAddress.postal_code || shippingAddress.zip || '',
          country: shippingAddress.country || 'India'
        }
      });
    }
  }, [user]);

  // Force cart refresh when page loads to ensure we have latest data
  useEffect(() => {
    if (!hasRefreshedCart && !migrating && user?.id) {
      console.log('🔄 Forcing cart refresh in CheckoutAddressPage...');
      setHasRefreshedCart(true);
      refreshCart().then(() => {
        console.log('✅ Cart refreshed in CheckoutAddressPage');
      }).catch(error => {
        console.error('❌ Failed to refresh cart in CheckoutAddressPage:', error);
      });
    }
  }, [hasRefreshedCart, migrating, user?.id, refreshCart]);

  // Show migration status and redirect if cart is empty (but wait for migration to complete)
  useEffect(() => {
    if (migrating) {
      console.log('🔄 Cart migration in progress in CheckoutAddressPage');
      return;
    }

    // Wait longer for cart state to stabilize after migration
    const checkCartWithDelay = setTimeout(() => {
      console.log('🔍 Checking cart in CheckoutAddressPage:', {
        itemsLength: items?.length,
        migrating,
        hasItems: !!(items && items.length > 0),
        hasCheckedCart
      });

      if (!hasCheckedCart) {
        setHasCheckedCart(true);

        if (!items || items.length === 0) {
          // Try one more time with a longer delay before giving up
          console.log('⚠️ Cart appears empty, trying one more time...');
          setTimeout(() => {
            if (!items || items.length === 0) {
              console.log('⚠️ Cart is still empty after final check, redirecting to cart');
              toast.error('Your cart appears to be empty. Please add items and try again.');
              navigate('/cart');
            } else {
              console.log('✅ Cart has items after final check:', items.length);
            }
          }, 1000);
        } else {
          console.log('✅ Cart has items in CheckoutAddressPage:', items.length);
        }
      }
    }, 1000); // Increased delay to 1000ms to allow state updates

    return () => clearTimeout(checkCartWithDelay);
  }, [items, navigate, migrating]);

  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }

    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const validateForm = () => {
    const errors = {};

    // Basic profile validation
    if (!formData.first_name?.trim()) errors.first_name = 'First name is required';
    if (!formData.last_name?.trim()) errors.last_name = 'Last name is required';
    if (!formData.email?.trim()) {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }
    if (!formData.phone?.trim()) errors.phone = 'Phone number is required';

    // Address validation - all fields required for checkout
    if (!formData.shipping_address.line1?.trim()) errors.line1 = 'Address Line 1 is required';
    if (!formData.shipping_address.city?.trim()) errors.city = 'City is required';
    if (!formData.shipping_address.state?.trim()) errors.state = 'State is required';
    if (!formData.shipping_address.postal_code?.trim()) errors.postal_code = 'Postal Code is required';
    if (!formData.shipping_address.country?.trim()) errors.country = 'Country is required';

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };



  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSaving(true);

    try {
      // Prepare data for profile update
      // Send both formats to ensure compatibility with both ProfileController and OTPAuthController
      // ProfileController expects 'name' field, OTPAuthController expects separate first_name/last_name
      const updateData = {
        name: `${formData.first_name.trim()} ${formData.last_name.trim()}`.trim(), // For ProfileController
        first_name: formData.first_name.trim(), // For OTPAuthController
        last_name: formData.last_name.trim(), // For OTPAuthController
        email: formData.email.trim(),
        phone: formData.phone.trim(),
        address_data: {
          first_name: formData.first_name.trim(),
          last_name: formData.last_name.trim(),
          address_line_1: formData.shipping_address.line1.trim(),
          address_line_2: formData.shipping_address.line2?.trim() || '',
          city: formData.shipping_address.city.trim(),
          state: formData.shipping_address.state.trim(),
          postal_code: formData.shipping_address.postal_code.trim(),
          country: formData.shipping_address.country.trim(),
          type: 'shipping',
          is_default: true
        }
      };

      console.log('🔄 Saving address and profile data:', updateData);

      const result = await updateProfile(updateData);

      if (result.success) {
        toast.success('Address saved successfully!');
        // Navigate to payment method selection page
        console.log('🔄 Navigating to payment page...');

        // Force a small delay to ensure state updates are processed
        setTimeout(() => {
          navigate('/checkout/payment', { replace: true });
          // Force a page refresh if needed
          window.location.hash = '#payment';
        }, 100);
      } else {
        // Handle validation errors from backend
        if (result.validation_errors) {
          const backendErrors = {};
          Object.entries(result.validation_errors).forEach(([field, messages]) => {
            const errorMessage = Array.isArray(messages) ? messages[0] : messages;

            if (field.startsWith('address_data.')) {
              const addressField = field.replace('address_data.', '');
              const frontendField = addressField === 'address_line_1' ? 'line1' :
                                  addressField === 'address_line_2' ? 'line2' :
                                  addressField;
              backendErrors[frontendField] = errorMessage;
            } else {
              backendErrors[field] = errorMessage;
            }
          });

          setFieldErrors(backendErrors);
          toast.error('Please correct the errors below');
        } else {
          toast.error(result.message || 'Failed to save address');
        }
      }
    } catch (error) {
      console.error('Address save error:', error);
      toast.error('An error occurred while saving your address');
    } finally {
      setIsSaving(false);
    }
  };



  // Payment functions moved to CheckoutPaymentPage

  const handleBackToCart = () => {
    navigate('/cart');
  };

  // Show loading state during cart migration
  if (migrating) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <div className="bg-[#2a2a2a] p-6 rounded-full">
              <div className="w-8 h-8 border-2 border-[#FF6F35] border-t-transparent rounded-full animate-spin" />
            </div>
          </div>
          <h2 className="text-xl sm:text-2xl font-bold text-white mb-4">
            Preparing Your Cart
          </h2>
          <p className="text-gray-400 text-sm sm:text-base">
            Please wait while we sync your cart...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <button
            onClick={handleBackToCart}
            className="p-2 hover:bg-[#2a2a2a] rounded-lg transition-colors"
          >
            <ArrowLeft size={24} />
          </button>
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold">Checkout</h1>
            <p className="text-gray-400 text-sm sm:text-base">Complete your address details</p>
          </div>
        </div>

        <motion.form
          onSubmit={handleSubmit}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-8"
        >
          {/* Order Summary */}
          <div className="bg-[#1a1a1a] rounded-lg p-6 border border-[#333333]">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <MapPin size={20} className="text-[#FF6F35]" />
              Order Summary
            </h3>

          <div className="space-y-4 mb-6">
            {items?.map((item, index) => {
              // Use sale price if available, otherwise use regular price (matching cart logic)
              const effectivePrice = parseFloat(item.salePrice || item.sale_price || item.price) || 0;
              const quantity = parseInt(item.quantity) || 1;
              const itemTotal = effectivePrice * quantity;

              return (
                <div key={`${item.id}-${item.color}-${item.size}-${index}`} className="flex gap-3 sm:gap-4 p-3 sm:p-4 bg-[#2a2a2a] rounded-lg hover:bg-[#333333] transition-colors">
                  {/* Item Image */}
                  <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gray-800 rounded-lg overflow-hidden flex-shrink-0 shadow-lg">
                    {item.image ? (
                      <img
                        src={optimizeImageUrl(item.image, 200, 200, 'good')}
                        alt={item.name}
                        className="w-full h-full object-cover transition-all duration-300"
                        loading="lazy"
                        style={{
                          filter: 'brightness(0.9)',
                          transition: 'filter 0.3s ease'
                        }}
                        onLoad={(e) => {
                          e.target.style.filter = 'brightness(1)';
                        }}
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'flex';
                        }}
                      />
                    ) : null}
                    <div className="w-full h-full bg-gray-700 flex items-center justify-center text-gray-400 text-xs" style={{display: item.image ? 'none' : 'flex'}}>
                      <Package size={16} className="text-gray-500" />
                    </div>
                  </div>

                  {/* Item Details */}
                  <div className="flex-1 min-w-0">
                    <h4 className="text-white font-medium text-sm sm:text-base mb-2 leading-tight">
                      {/* Mobile: Show shorter name, Desktop: Show longer name */}
                      <span className="sm:hidden">
                        {item.name?.length > 20 ? `${item.name.substring(0, 20)}...` : item.name || 'Product'}
                      </span>
                      <span className="hidden sm:block">
                        {item.name?.length > 35 ? `${item.name.substring(0, 35)}...` : item.name || 'Product'}
                      </span>
                    </h4>

                    {/* Mobile: Stack vertically, Desktop: Horizontal */}
                    <div className="space-y-1 sm:space-y-0 sm:flex sm:items-center sm:gap-3 text-xs sm:text-sm text-[#AAAAAA]">
                      {item.color && (
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full border border-[#404040] flex-shrink-0"
                            style={{
                              backgroundColor: !item.color || item.color === 'Default' ? '#6b7280' : (item.color?.toLowerCase?.()?.replace(' ', '') || '#6b7280'),
                              opacity: !item.color || item.color === 'Default' ? 0.5 : 1
                            }}
                          ></div>
                          <span>{item.color}</span>
                        </div>
                      )}
                      {item.size && (
                        <div className="flex items-center gap-1">
                          <span>Size:</span>
                          <span className="font-medium text-white">{item.size}</span>
                        </div>
                      )}
                      <div className="flex items-center gap-1">
                        <span>Qty:</span>
                        <span className="font-medium text-white">{quantity}</span>
                      </div>
                    </div>
                  </div>

                  {/* Price */}
                  <div className="text-right flex-shrink-0">
                    <div className="text-white font-semibold text-sm sm:text-base">
                      ₹{itemTotal.toLocaleString()}
                    </div>
                    {item.salePrice && item.price && parseFloat(item.salePrice) < parseFloat(item.price) && (
                      <div className="text-gray-500 text-xs line-through">
                        ₹{(parseFloat(item.price) * quantity).toLocaleString()}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Order Total */}
          <div className="border-t border-[#333333] pt-4 space-y-2">
            <div className="flex justify-between text-gray-400">
              <span>Subtotal</span>
              <span>₹{calculateSubtotal().toLocaleString()}</span>
            </div>
            <div className="flex justify-between text-gray-400">
              <span>Shipping</span>
              <span className="text-green-400">Free</span>
            </div>
            <div className="flex justify-between text-white font-semibold text-lg border-t border-[#333333] pt-2">
              <span>Total</span>
              <span>₹{calculateTotal().toLocaleString()}</span>
            </div>
          </div>
        </div>

          {/* Personal Information */}
          <div className="bg-[#1a1a1a] rounded-lg p-6 border border-[#333333]">
            <h3 className="text-lg font-semibold mb-4">Personal Information</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-gray-400 text-sm font-medium mb-2">
                  First Name <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.first_name}
                  onChange={(e) => handleInputChange('first_name', e.target.value)}
                  className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.first_name ? 'border-red-500' : 'border-gray-600'}`}
                  placeholder="Enter first name"
                />
                {fieldErrors.first_name && <p className="text-red-400 text-xs mt-1">{fieldErrors.first_name}</p>}
              </div>

              <div>
                <label className="block text-gray-400 text-sm font-medium mb-2">
                  Last Name <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.last_name}
                  onChange={(e) => handleInputChange('last_name', e.target.value)}
                  className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.last_name ? 'border-red-500' : 'border-gray-600'}`}
                  placeholder="Enter last name"
                />
                {fieldErrors.last_name && <p className="text-red-400 text-xs mt-1">{fieldErrors.last_name}</p>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-400 text-sm font-medium mb-2">
                  Email <span className="text-red-400">*</span>
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.email ? 'border-red-500' : 'border-gray-600'}`}
                  placeholder="Enter email address"
                />
                {fieldErrors.email && <p className="text-red-400 text-xs mt-1">{fieldErrors.email}</p>}
              </div>

              <div>
                <label className="block text-gray-400 text-sm font-medium mb-2">
                  Phone Number <span className="text-red-400">*</span>
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.phone ? 'border-red-500' : 'border-gray-600'}`}
                  placeholder="Enter phone number"
                />
                {fieldErrors.phone && <p className="text-red-400 text-xs mt-1">{fieldErrors.phone}</p>}
              </div>
            </div>
          </div>

          {/* Shipping Address */}
          <div className="bg-[#1a1a1a] rounded-lg p-6 border border-[#333333]">
            <h3 className="text-lg font-semibold mb-4">Shipping Address</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-gray-400 text-sm font-medium mb-2">
                  Address Line 1 <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  value={formData.shipping_address.line1}
                  onChange={(e) => handleInputChange('shipping_address.line1', e.target.value)}
                  className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.line1 ? 'border-red-500' : 'border-gray-600'}`}
                  placeholder="Street address"
                />
                {fieldErrors.line1 && <p className="text-red-400 text-xs mt-1">{fieldErrors.line1}</p>}
              </div>

              <div>
                <label className="block text-gray-400 text-sm font-medium mb-2">
                  Address Line 2
                </label>
                <input
                  type="text"
                  value={formData.shipping_address.line2}
                  onChange={(e) => handleInputChange('shipping_address.line2', e.target.value)}
                  className="w-full px-4 py-3 bg-black border border-gray-600 rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all"
                  placeholder="Apartment, suite, etc. (optional)"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-400 text-sm font-medium mb-2">
                    City <span className="text-red-400">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.shipping_address.city}
                    onChange={(e) => handleInputChange('shipping_address.city', e.target.value)}
                    className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.city ? 'border-red-500' : 'border-gray-600'}`}
                    placeholder="Enter city"
                  />
                  {fieldErrors.city && <p className="text-red-400 text-xs mt-1">{fieldErrors.city}</p>}
                </div>

                <div>
                  <label className="block text-gray-400 text-sm font-medium mb-2">
                    State <span className="text-red-400">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.shipping_address.state}
                    onChange={(e) => handleInputChange('shipping_address.state', e.target.value)}
                    className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.state ? 'border-red-500' : 'border-gray-600'}`}
                    placeholder="Enter state"
                  />
                  {fieldErrors.state && <p className="text-red-400 text-xs mt-1">{fieldErrors.state}</p>}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-400 text-sm font-medium mb-2">
                    Postal Code <span className="text-red-400">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.shipping_address.postal_code}
                    onChange={(e) => handleInputChange('shipping_address.postal_code', e.target.value)}
                    className={`w-full px-4 py-3 bg-black border rounded-lg text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.postal_code ? 'border-red-500' : 'border-gray-600'}`}
                    placeholder="Enter postal code"
                  />
                  {fieldErrors.postal_code && <p className="text-red-400 text-xs mt-1">{fieldErrors.postal_code}</p>}
                </div>

                <div>
                  <label className="block text-gray-400 text-sm font-medium mb-2">
                    Country <span className="text-red-400">*</span>
                  </label>
                  <select
                    value={formData.shipping_address.country}
                    onChange={(e) => handleInputChange('shipping_address.country', e.target.value)}
                    className={`w-full px-4 py-3 bg-black border rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#FF6F35] transition-all ${fieldErrors.country ? 'border-red-500' : 'border-gray-600'}`}
                  >
                    <option value="India">India</option>
                  </select>
                  {fieldErrors.country && <p className="text-red-400 text-xs mt-1">{fieldErrors.country}</p>}
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSaving || isLoading || migrating}
            className="w-full bg-[#FF6F35] hover:bg-[#FF6F35]/90 text-white font-medium py-4 px-6 rounded-lg transition-all duration-300 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {migrating ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                Preparing Cart...
              </>
            ) : isSaving ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                Saving Address...
              </>
            ) : (
              <>
                <ArrowRight size={20} />
                Save & Continue
              </>
            )}
          </button>
        </motion.form>
      </div>
    </div>
  );
};

export default CheckoutAddressPage;

