import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { CheckCircle, Package, MapPin, Calendar, ArrowRight, Home } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useCart } from '../context/CartContext';
import { dataService } from '../services/dataService';

const OrderSuccessPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user } = useAuth();
  const { items: cartItems, clearCart } = useCart();
  const [orderData, setOrderData] = useState(null);

  // Clear cart on successful order (if not already cleared)
  useEffect(() => {
    if (orderData && cartItems.length > 0) {
      console.log('🧹 Clearing cart on order success page');
      clearCart();
    }
  }, [orderData, cartItems.length, clearCart]);

  // Get order data from URL params or localStorage
  useEffect(() => {
    const orderId = searchParams.get('order_id');
    const orderNumber = searchParams.get('order_number');
    const paymentMethod = searchParams.get('payment_method');
    const totalAmount = searchParams.get('total_amount');

    console.log('🔄 OrderSuccessPage - URL params:', { orderId, orderNumber, paymentMethod, totalAmount });

    // Try to get order data from localStorage (set during order creation)
    const storedOrderData = localStorage.getItem('lastOrderData');
    console.log('🔄 OrderSuccessPage - Stored data:', storedOrderData);

    if (storedOrderData) {
      try {
        const parsedData = JSON.parse(storedOrderData);
        console.log('🔄 OrderSuccessPage - Parsed stored data:', parsedData);
        setOrderData(parsedData);
        // Clear the stored data after using it
        localStorage.removeItem('lastOrderData');
      } catch (error) {
        console.error('Error parsing stored order data:', error);
        // Fallback to URL params if localStorage parsing fails
        if (orderId || orderNumber) {
          setOrderData({
            id: orderId,
            order_number: orderNumber,
            payment_method: paymentMethod,
            total_amount: totalAmount,
            items: [], // Empty items array
            shipping_address: null // No address data
          });
        }
      }
    } else if (orderId || orderNumber) {
      // Fallback: try to fetch order details from backend
      console.log('🔄 OrderSuccessPage - Trying to fetch order from backend');
      const fetchOrderDetails = async () => {
        try {
          const orderDetails = await dataService.getOrder(orderId);
          console.log('🔄 OrderSuccessPage - Fetched order details:', orderDetails);

          // If we got order details, use them
          if (orderDetails && (orderDetails.items || orderDetails.order_items)) {
            setOrderData({
              id: orderId,
              order_number: orderNumber,
              payment_method: paymentMethod,
              total_amount: totalAmount,
              items: orderDetails.items || orderDetails.order_items || [],
              shipping_address: orderDetails.shipping_address || orderDetails.address || null
            });
          } else {
            // Try to get user's recent orders to find this order
            const userOrders = await dataService.getOrders(user?.id);
            const recentOrder = userOrders?.orders?.find(order =>
              order.id == orderId || order.order_number === orderNumber
            );

            console.log('🔄 OrderSuccessPage - User orders:', userOrders);
            console.log('🔄 OrderSuccessPage - Looking for order:', { orderId, orderNumber });
            console.log('🔄 OrderSuccessPage - Found recent order:', recentOrder);

            if (recentOrder) {
              console.log('🔄 OrderSuccessPage - Found order in user orders:', recentOrder);
              setOrderData({
                id: orderId,
                order_number: orderNumber,
                payment_method: paymentMethod,
                total_amount: totalAmount,
                items: recentOrder.items || [],
                shipping_address: recentOrder.shipping_address || null
              });
            } else {
              throw new Error('Order not found');
            }
          }
        } catch (error) {
          console.error('Failed to fetch order details:', error);
          // Final fallback: create minimal order data from URL params
          setOrderData({
            id: orderId,
            order_number: orderNumber,
            payment_method: paymentMethod,
            total_amount: totalAmount,
            items: [], // Empty items array
            shipping_address: null // No address data
          });
        }
      };

      fetchOrderDetails();
    }
  }, [searchParams]);

  // Calculate delivery date (1 week from now)
  const getDeliveryDate = () => {
    const deliveryDate = new Date();
    deliveryDate.setDate(deliveryDate.getDate() + 7);
    return deliveryDate.toLocaleDateString('en-IN', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleContinueShopping = () => {
    // Use setTimeout to ensure any animations complete before navigation
    setTimeout(() => {
      navigate('/', { replace: true });
    }, 100);
  };

  // Handle navigation to other pages
  const handleNavigation = (path) => {
    navigate(path, { replace: true });
  };

  const handleViewOrders = () => {
    console.log('🔄 Navigating to profile page from order success');
    // Use setTimeout to ensure any animations complete before navigation
    setTimeout(() => {
      console.log('🔄 Executing navigation to /profile');
      navigate('/profile', { replace: true });
    }, 100);
  };

  if (!orderData) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF6F35] mx-auto mb-4"></div>
          <p>Loading order details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Success Banner */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center justify-center w-20 h-20 bg-green-500 rounded-full mb-4">
            <CheckCircle size={40} className="text-white" />
          </div>
          <h1 className="text-3xl sm:text-4xl font-bold mb-2">Order Confirmed!</h1>
          <p className="text-gray-400 text-lg">
            Thank you for your purchase. Your order has been successfully placed.
          </p>
          {orderData.order_number && (
            <p className="text-[#FF6F35] font-semibold mt-2">
              Order #{orderData.order_number}
            </p>
          )}
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Order Summary */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-[#1a1a1a] rounded-lg p-6 border border-[#333333]"
          >
            <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <Package size={20} className="text-[#FF6F35]" />
              Order Summary
            </h2>
            
            {orderData.items && orderData.items.length > 0 ? (
              <div className="space-y-4">
                {orderData.items.map((item, index) => {
                  // Handle different field names from localStorage vs API
                  const itemName = item.name || item.product_name || 'Product';
                  const itemColor = item.color || item.selected_color || '';
                  const itemSize = item.size || item.selected_size || '';
                  const itemImage = item.image || item.product_image || '';
                  const itemSku = item.sku || item.product_sku || '';
                  const itemPrice = item.sale_price || item.unit_price || item.price || 0;
                  const itemQuantity = item.quantity || 1;

                  return (
                    <div key={index} className="flex gap-3 p-3 bg-[#2a2a2a] rounded-lg">
                      <div className="w-16 h-16 bg-gray-800 rounded-lg overflow-hidden flex-shrink-0">
                        {itemImage ? (
                          <img
                            src={itemImage}
                            alt={itemName}
                            className="w-full h-full object-cover"
                            loading="lazy"
                            style={{
                              filter: 'brightness(0.9)',
                              transition: 'filter 0.3s ease'
                            }}
                            onLoad={(e) => {
                              e.target.style.filter = 'brightness(1)';
                            }}
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-700 flex items-center justify-center">
                            <Package size={20} className="text-gray-500" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium text-sm line-clamp-2">{itemName}</h3>
                        <p className="text-gray-400 text-xs">
                          {itemColor && `${itemColor} • `}Size {itemSize} • Qty {itemQuantity}
                        </p>
                        <p className="text-[#FF6F35] font-semibold text-sm">
                          ₹{parseFloat(itemPrice).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-6">
                <p className="text-gray-400 mb-2">Your order has been confirmed!</p>
                <p className="text-sm text-gray-500">
                  Order details will be available in your profile section shortly.
                </p>
              </div>
            )}

            <div className="border-t border-[#333333] mt-4 pt-4">
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold">Total Amount</span>
                <span className="text-xl font-bold text-[#FF6F35]">
                  ₹{orderData.total_amount ? parseFloat(orderData.total_amount).toLocaleString() : '0'}
                </span>
              </div>
              <p className="text-sm text-gray-400 mt-1">
                Payment Method: {orderData.payment_method === 'cod' ? 'Cash on Delivery' : 'Online Payment'}
              </p>
            </div>
          </motion.div>

          {/* Shipping & Delivery Info */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-6"
          >
            {/* Shipping Address */}
            <div className="bg-[#1a1a1a] rounded-lg p-6 border border-[#333333]">
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                <MapPin size={20} className="text-[#FF6F35]" />
                Shipping Address
              </h2>
              {orderData.shipping_address || orderData.customer_name ? (
                <div className="text-gray-300">
                  <p className="font-medium">
                    {orderData.customer_name ||
                     (orderData.shipping_address?.first_name && orderData.shipping_address?.last_name
                       ? `${orderData.shipping_address.first_name} ${orderData.shipping_address.last_name}`
                       : `${user?.first_name || ''} ${user?.last_name || ''}`)}
                  </p>
                  <p>
                    {orderData.shipping_address?.line1 ||
                     orderData.shipping_address?.address_line_1 ||
                     orderData.shipping_address_line1 ||
                     'Address not available'}
                  </p>
                  {(orderData.shipping_address?.line2 ||
                    orderData.shipping_address?.address_line_2 ||
                    orderData.shipping_address_line2) && (
                    <p>
                      {orderData.shipping_address.line2 ||
                       orderData.shipping_address.address_line_2 ||
                       orderData.shipping_address_line2}
                    </p>
                  )}
                  <p>
                    {orderData.shipping_address?.city || orderData.shipping_city || 'City'}, {' '}
                    {orderData.shipping_address?.state || orderData.shipping_state || 'State'}
                  </p>
                  <p>{orderData.shipping_address?.postal_code || orderData.shipping_postal_code || 'Postal Code'}</p>
                  <p>{orderData.shipping_address?.country || orderData.shipping_country || 'India'}</p>
                </div>
              ) : user?.addresses && user.addresses.length > 0 ? (
                <div className="text-gray-300">
                  <p className="font-medium">{user?.first_name} {user?.last_name}</p>
                  <p>{user.addresses[0].address_line_1 || user.addresses[0].line1}</p>
                  {(user.addresses[0].address_line_2 || user.addresses[0].line2) &&
                    <p>{user.addresses[0].address_line_2 || user.addresses[0].line2}</p>}
                  <p>{user.addresses[0].city}, {user.addresses[0].state}</p>
                  <p>{user.addresses[0].postal_code}</p>
                  <p>{user.addresses[0].country || 'India'}</p>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-gray-400 mb-1">Shipping address confirmed</p>
                  <p className="text-sm text-gray-500">
                    Your order will be shipped to your registered address.
                  </p>
                </div>
              )}
            </div>

            {/* Delivery Information */}
            <div className="bg-[#1a1a1a] rounded-lg p-6 border border-[#333333]">
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                <Calendar size={20} className="text-[#FF6F35]" />
                Delivery Information
              </h2>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 bg-[#2a2a2a] rounded-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <div>
                    <p className="font-medium text-green-400">Estimated Delivery</p>
                    <p className="text-sm text-gray-300">Delivery by {getDeliveryDate()}</p>
                  </div>
                </div>
                <div className="text-sm text-gray-400">
                  <p>• Free shipping on all orders</p>
                  <p>• Track your order in the Profile section</p>
                  <p>• Contact support for any queries</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="flex flex-col sm:flex-row gap-4 mt-8 justify-center"
        >
          <button
            onClick={handleViewOrders}
            className="flex items-center justify-center gap-2 bg-[#FF6F35] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#e55a2b] transition-colors"
          >
            <Package size={20} />
            View My Orders
            <ArrowRight size={20} />
          </button>
          <button
            onClick={handleContinueShopping}
            className="flex items-center justify-center gap-2 bg-transparent border border-[#333333] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#1a1a1a] transition-colors"
          >
            <Home size={20} />
            Continue Shopping
          </button>
        </motion.div>
      </div>
    </div>
  );
};

export default OrderSuccessPage;
