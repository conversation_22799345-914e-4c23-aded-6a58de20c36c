import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';

/**
 * Custom hook for handling complete logout process
 * Clears localStorage and lets contexts handle data clearing automatically
 */
export const useLogout = () => {
  const { logout } = useAuth();
  const navigate = useNavigate();

  const handleCompleteLogout = async (redirectTo = '/') => {
    try {
      // Clear localStorage first to prevent data leakage
      localStorage.removeItem('cart');
      localStorage.removeItem('wishlist');
      localStorage.removeItem('wolffoxx-outfits');
      localStorage.removeItem('savedOutfits');
      localStorage.removeItem('currentOutfit');

      // Then logout from auth (this also clears auth localStorage)
      await logout();

      // Use window.location.href to ensure clean navigation without /login in URL
      window.location.href = redirectTo;
    } catch (error) {
      console.error('Logout error:', error);
      // Even if logout fails, clear local data and navigate
      localStorage.removeItem('cart');
      localStorage.removeItem('wishlist');
      localStorage.removeItem('wolffoxx-outfits');
      localStorage.removeItem('savedOutfits');
      localStorage.removeItem('currentOutfit');
      // Use window.location.href for clean navigation
      window.location.href = redirectTo;
    }
  };

  return { handleCompleteLogout };
};
